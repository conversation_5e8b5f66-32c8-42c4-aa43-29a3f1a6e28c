{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/context/FlashcardContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { FlashcardContextType, FlashcardSet, Flashcard } from '@/types/flashcard';\n\n// Action types\ntype FlashcardAction =\n  | { type: 'SET_SETS'; payload: FlashcardSet[] }\n  | { type: 'CREATE_SET'; payload: { name: string; description?: string } }\n  | { type: 'UPDATE_SET'; payload: { id: string; updates: Partial<Omit<FlashcardSet, 'id' | 'flashcards' | 'createdAt'>> } }\n  | { type: 'DELETE_SET'; payload: string }\n  | { type: 'SET_CURRENT_SET'; payload: string | null }\n  | { type: 'CREATE_FLASHCARD'; payload: { setId: string; front: string; back: string } }\n  | { type: 'UPDATE_FLASHCARD'; payload: { setId: string; cardId: string; updates: Partial<Omit<Flashcard, 'id' | 'createdAt'>> } }\n  | { type: 'DELETE_FLASHCARD'; payload: { setId: string; cardId: string } };\n\ninterface FlashcardState {\n  sets: FlashcardSet[];\n  currentSet: FlashcardSet | null;\n}\n\n// Utility functions\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\nconst saveToLocalStorage = (sets: FlashcardSet[]) => {\n  if (typeof window !== 'undefined') {\n    localStorage.setItem('flashcard-sets', JSON.stringify(sets));\n  }\n};\n\nconst loadFromLocalStorage = (): FlashcardSet[] => {\n  if (typeof window !== 'undefined') {\n    const stored = localStorage.getItem('flashcard-sets');\n    if (stored) {\n      try {\n        const parsed = JSON.parse(stored);\n        return parsed.map((set: any) => ({\n          ...set,\n          createdAt: new Date(set.createdAt),\n          updatedAt: new Date(set.updatedAt),\n          flashcards: set.flashcards.map((card: any) => ({\n            ...card,\n            createdAt: new Date(card.createdAt),\n            updatedAt: new Date(card.updatedAt),\n          })),\n        }));\n      } catch (error) {\n        console.error('Error parsing stored flashcard sets:', error);\n      }\n    }\n  }\n  return [];\n};\n\n// Reducer\nconst flashcardReducer = (state: FlashcardState, action: FlashcardAction): FlashcardState => {\n  switch (action.type) {\n    case 'SET_SETS':\n      return {\n        ...state,\n        sets: action.payload,\n      };\n\n    case 'CREATE_SET': {\n      const newSet: FlashcardSet = {\n        id: generateId(),\n        name: action.payload.name,\n        description: action.payload.description,\n        flashcards: [],\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n      const newSets = [...state.sets, newSet];\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n      };\n    }\n\n    case 'UPDATE_SET': {\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.id\n          ? { ...set, ...action.payload.updates, updatedAt: new Date() }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.id\n          ? newSets.find(set => set.id === action.payload.id) || null\n          : state.currentSet,\n      };\n    }\n\n    case 'DELETE_SET': {\n      const newSets = state.sets.filter(set => set.id !== action.payload);\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload ? null : state.currentSet,\n      };\n    }\n\n    case 'SET_CURRENT_SET':\n      return {\n        ...state,\n        currentSet: action.payload ? state.sets.find(set => set.id === action.payload) || null : null,\n      };\n\n    case 'CREATE_FLASHCARD': {\n      const newCard: Flashcard = {\n        id: generateId(),\n        front: action.payload.front,\n        back: action.payload.back,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.setId\n          ? {\n              ...set,\n              flashcards: [...set.flashcards, newCard],\n              updatedAt: new Date(),\n            }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.setId\n          ? newSets.find(set => set.id === action.payload.setId) || null\n          : state.currentSet,\n      };\n    }\n\n    case 'UPDATE_FLASHCARD': {\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.setId\n          ? {\n              ...set,\n              flashcards: set.flashcards.map(card =>\n                card.id === action.payload.cardId\n                  ? { ...card, ...action.payload.updates, updatedAt: new Date() }\n                  : card\n              ),\n              updatedAt: new Date(),\n            }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.setId\n          ? newSets.find(set => set.id === action.payload.setId) || null\n          : state.currentSet,\n      };\n    }\n\n    case 'DELETE_FLASHCARD': {\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.setId\n          ? {\n              ...set,\n              flashcards: set.flashcards.filter(card => card.id !== action.payload.cardId),\n              updatedAt: new Date(),\n            }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.setId\n          ? newSets.find(set => set.id === action.payload.setId) || null\n          : state.currentSet,\n      };\n    }\n\n    default:\n      return state;\n  }\n};\n\n// Context\nconst FlashcardContext = createContext<FlashcardContextType | undefined>(undefined);\n\n// Provider component\nexport const FlashcardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(flashcardReducer, {\n    sets: [],\n    currentSet: null,\n  });\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const storedSets = loadFromLocalStorage();\n    dispatch({ type: 'SET_SETS', payload: storedSets });\n  }, []);\n\n  const contextValue: FlashcardContextType = {\n    sets: state.sets,\n    currentSet: state.currentSet,\n    createSet: (name: string, description?: string) => {\n      dispatch({ type: 'CREATE_SET', payload: { name, description } });\n    },\n    updateSet: (id: string, updates) => {\n      dispatch({ type: 'UPDATE_SET', payload: { id, updates } });\n    },\n    deleteSet: (id: string) => {\n      dispatch({ type: 'DELETE_SET', payload: id });\n    },\n    setCurrentSet: (setId: string | null) => {\n      dispatch({ type: 'SET_CURRENT_SET', payload: setId });\n    },\n    createFlashcard: (setId: string, front: string, back: string) => {\n      dispatch({ type: 'CREATE_FLASHCARD', payload: { setId, front, back } });\n    },\n    updateFlashcard: (setId: string, cardId: string, updates) => {\n      dispatch({ type: 'UPDATE_FLASHCARD', payload: { setId, cardId, updates } });\n    },\n    deleteFlashcard: (setId: string, cardId: string) => {\n      dispatch({ type: 'DELETE_FLASHCARD', payload: { setId, cardId } });\n    },\n  };\n\n  return (\n    <FlashcardContext.Provider value={contextValue}>\n      {children}\n    </FlashcardContext.Provider>\n  );\n};\n\n// Hook to use the context\nexport const useFlashcards = () => {\n  const context = useContext(FlashcardContext);\n  if (context === undefined) {\n    throw new Error('useFlashcards must be used within a FlashcardProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAqBA,oBAAoB;AACpB,MAAM,aAAa,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAE9D,MAAM,qBAAqB,CAAC;IAC1B,uCAAmC;;IAEnC;AACF;AAEA,MAAM,uBAAuB;IAC3B,uCAAmC;;IAmBnC;IACA,OAAO,EAAE;AACX;AAEA,UAAU;AACV,MAAM,mBAAmB,CAAC,OAAuB;IAC/C,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO;YACtB;QAEF,KAAK;YAAc;gBACjB,MAAM,SAAuB;oBAC3B,IAAI;oBACJ,MAAM,OAAO,OAAO,CAAC,IAAI;oBACzB,aAAa,OAAO,OAAO,CAAC,WAAW;oBACvC,YAAY,EAAE;oBACd,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBACA,MAAM,UAAU;uBAAI,MAAM,IAAI;oBAAE;iBAAO;gBACvC,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;gBACR;YACF;QAEA,KAAK;YAAc;gBACjB,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACxB;wBAAE,GAAG,GAAG;wBAAE,GAAG,OAAO,OAAO,CAAC,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC3D;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,EAAE,GAClD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,OACrD,MAAM,UAAU;gBACtB;YACF;QAEA,KAAK;YAAc;gBACjB,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO;gBAClE,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,GAAG,OAAO,MAAM,UAAU;gBAC/E;YACF;QAEA,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY,OAAO,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,KAAK,OAAO;YAC3F;QAEF,KAAK;YAAoB;gBACvB,MAAM,UAAqB;oBACzB,IAAI;oBACJ,OAAO,OAAO,OAAO,CAAC,KAAK;oBAC3B,MAAM,OAAO,OAAO,CAAC,IAAI;oBACzB,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBAEA,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,GAC3B;wBACE,GAAG,GAAG;wBACN,YAAY;+BAAI,IAAI,UAAU;4BAAE;yBAAQ;wBACxC,WAAW,IAAI;oBACjB,IACA;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,KAAK,GACrD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,OACxD,MAAM,UAAU;gBACtB;YACF;QAEA,KAAK;YAAoB;gBACvB,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,GAC3B;wBACE,GAAG,GAAG;wBACN,YAAY,IAAI,UAAU,CAAC,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,MAAM,GAC7B;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO,OAAO,CAAC,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAC5D;wBAEN,WAAW,IAAI;oBACjB,IACA;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,KAAK,GACrD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,OACxD,MAAM,UAAU;gBACtB;YACF;QAEA,KAAK;YAAoB;gBACvB,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,GAC3B;wBACE,GAAG,GAAG;wBACN,YAAY,IAAI,UAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,MAAM;wBAC3E,WAAW,IAAI;oBACjB,IACA;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,KAAK,GACrD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,OACxD,MAAM,UAAU;gBACtB;YACF;QAEA;YACE,OAAO;IACX;AACF;AAEA,UAAU;AACV,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAGlE,MAAM,oBAA6D,CAAC,EAAE,QAAQ,EAAE;IACrF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;QACrD,MAAM,EAAE;QACR,YAAY;IACd;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;QACnB,SAAS;YAAE,MAAM;YAAY,SAAS;QAAW;IACnD,GAAG,EAAE;IAEL,MAAM,eAAqC;QACzC,MAAM,MAAM,IAAI;QAChB,YAAY,MAAM,UAAU;QAC5B,WAAW,CAAC,MAAc;YACxB,SAAS;gBAAE,MAAM;gBAAc,SAAS;oBAAE;oBAAM;gBAAY;YAAE;QAChE;QACA,WAAW,CAAC,IAAY;YACtB,SAAS;gBAAE,MAAM;gBAAc,SAAS;oBAAE;oBAAI;gBAAQ;YAAE;QAC1D;QACA,WAAW,CAAC;YACV,SAAS;gBAAE,MAAM;gBAAc,SAAS;YAAG;QAC7C;QACA,eAAe,CAAC;YACd,SAAS;gBAAE,MAAM;gBAAmB,SAAS;YAAM;QACrD;QACA,iBAAiB,CAAC,OAAe,OAAe;YAC9C,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAO;oBAAO;gBAAK;YAAE;QACvE;QACA,iBAAiB,CAAC,OAAe,QAAgB;YAC/C,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAO;oBAAQ;gBAAQ;YAAE;QAC3E;QACA,iBAAiB,CAAC,OAAe;YAC/B,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAO;gBAAO;YAAE;QAClE;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAGO,MAAM,gBAAgB;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}