{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/components/FlashcardComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Flashcard } from '@/types/flashcard';\n\ninterface FlashcardComponentProps {\n  flashcard: Flashcard;\n  onEdit?: () => void;\n  onDelete?: () => void;\n  showActions?: boolean;\n  className?: string;\n}\n\nexport const FlashcardComponent: React.FC<FlashcardComponentProps> = ({\n  flashcard,\n  onEdit,\n  onDelete,\n  showActions = true,\n  className = '',\n}) => {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const handleFlip = () => {\n    setIsFlipped(!isFlipped);\n  };\n\n  const handleEdit = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    onEdit?.();\n  };\n\n  const handleDelete = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    if (window.confirm('Are you sure you want to delete this flashcard?')) {\n      onDelete?.();\n    }\n  };\n\n  return (\n    <div className={`flashcard-container ${className}`}>\n      <div\n        className={`flashcard ${isFlipped ? 'flipped' : ''}`}\n        onClick={handleFlip}\n      >\n        <div className=\"flashcard-inner\">\n          {/* Front side */}\n          <div className=\"flashcard-front\">\n            <div className=\"flashcard-content\">\n              <div className=\"text-sm text-gray-500 mb-2\">Front</div>\n              <div className=\"flashcard-text\">\n                {flashcard.front || 'Click to add front content'}\n              </div>\n            </div>\n            {showActions && (\n              <div className=\"flashcard-actions\">\n                <button\n                  onClick={handleEdit}\n                  className=\"action-btn edit-btn\"\n                  title=\"Edit flashcard\"\n                >\n                  ✏️\n                </button>\n                <button\n                  onClick={handleDelete}\n                  className=\"action-btn delete-btn\"\n                  title=\"Delete flashcard\"\n                >\n                  🗑️\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Back side */}\n          <div className=\"flashcard-back\">\n            <div className=\"flashcard-content\">\n              <div className=\"text-sm text-gray-500 mb-2\">Back</div>\n              <div className=\"flashcard-text\">\n                {flashcard.back || 'Click to add back content'}\n              </div>\n            </div>\n            {showActions && (\n              <div className=\"flashcard-actions\">\n                <button\n                  onClick={handleEdit}\n                  className=\"action-btn edit-btn\"\n                  title=\"Edit flashcard\"\n                >\n                  ✏️\n                </button>\n                <button\n                  onClick={handleDelete}\n                  className=\"action-btn delete-btn\"\n                  title=\"Delete flashcard\"\n                >\n                  🗑️\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"flip-hint\">\n        Click to flip\n      </div>\n\n      <style jsx>{`\n        .flashcard-container {\n          perspective: 1000px;\n          width: 300px;\n          height: 200px;\n          margin: 10px;\n        }\n\n        .flashcard {\n          position: relative;\n          width: 100%;\n          height: 100%;\n          cursor: pointer;\n          transition: transform 0.6s;\n          transform-style: preserve-3d;\n        }\n\n        .flashcard.flipped {\n          transform: rotateY(180deg);\n        }\n\n        .flashcard-inner {\n          position: relative;\n          width: 100%;\n          height: 100%;\n        }\n\n        .flashcard-front,\n        .flashcard-back {\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          backface-visibility: hidden;\n          border-radius: 12px;\n          border: 2px solid #e5e7eb;\n          background: white;\n          display: flex;\n          flex-direction: column;\n          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n          transition: all 0.3s ease;\n        }\n\n        .flashcard-front:hover,\n        .flashcard-back:hover {\n          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\n          border-color: #3b82f6;\n        }\n\n        .flashcard-back {\n          transform: rotateY(180deg);\n          background: #f8fafc;\n        }\n\n        .flashcard-content {\n          flex: 1;\n          padding: 20px;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          text-align: center;\n        }\n\n        .flashcard-text {\n          font-size: 16px;\n          line-height: 1.5;\n          color: #374151;\n          word-wrap: break-word;\n          overflow-wrap: break-word;\n          max-height: 120px;\n          overflow-y: auto;\n        }\n\n        .flashcard-actions {\n          position: absolute;\n          top: 8px;\n          right: 8px;\n          display: flex;\n          gap: 4px;\n          opacity: 0;\n          transition: opacity 0.2s ease;\n        }\n\n        .flashcard-front:hover .flashcard-actions,\n        .flashcard-back:hover .flashcard-actions {\n          opacity: 1;\n        }\n\n        .action-btn {\n          width: 28px;\n          height: 28px;\n          border: none;\n          border-radius: 6px;\n          background: rgba(255, 255, 255, 0.9);\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          transition: all 0.2s ease;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        }\n\n        .action-btn:hover {\n          transform: scale(1.1);\n          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n        }\n\n        .edit-btn:hover {\n          background: #dbeafe;\n        }\n\n        .delete-btn:hover {\n          background: #fee2e2;\n        }\n\n        .flip-hint {\n          text-align: center;\n          margin-top: 8px;\n          font-size: 12px;\n          color: #6b7280;\n          opacity: 0.7;\n        }\n\n        @media (max-width: 640px) {\n          .flashcard-container {\n            width: 280px;\n            height: 180px;\n          }\n\n          .flashcard-content {\n            padding: 16px;\n          }\n\n          .flashcard-text {\n            font-size: 14px;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAaO,MAAM,qBAAwD,CAAC,EACpE,SAAS,EACT,MAAM,EACN,QAAQ,EACR,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB,aAAa,CAAC;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,IAAI,OAAO,OAAO,CAAC,oDAAoD;YACrE;QACF;IACF;IAEA,qBACE,8OAAC;kDAAe,CAAC,oBAAoB,EAAE,WAAW;;0BAChD,8OAAC;gBAEC,SAAS;0DADE,CAAC,UAAU,EAAE,YAAY,YAAY,IAAI;0BAGpD,cAAA,8OAAC;8DAAc;;sCAEb,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDAA6B;;;;;;sDAC5C,8OAAC;sFAAc;sDACZ,UAAU,KAAK,IAAI;;;;;;;;;;;;gCAGvB,6BACC,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDAA6B;;;;;;sDAC5C,8OAAC;sFAAc;sDACZ,UAAU,IAAI,IAAI;;;;;;;;;;;;gCAGtB,6BACC,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC;0DAAc;0BAAY;;;;;;;;;;;;;;;;AAiJjC", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/components/FlashcardModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Flashcard } from '@/types/flashcard';\n\ninterface FlashcardModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (front: string, back: string) => void;\n  flashcard?: Flashcard;\n  title?: string;\n}\n\nexport const FlashcardModal: React.FC<FlashcardModalProps> = ({\n  isOpen,\n  onClose,\n  onSave,\n  flashcard,\n  title = 'Create Flashcard',\n}) => {\n  const [front, setFront] = useState('');\n  const [back, setBack] = useState('');\n\n  useEffect(() => {\n    if (flashcard) {\n      setFront(flashcard.front);\n      setBack(flashcard.back);\n    } else {\n      setFront('');\n      setBack('');\n    }\n  }, [flashcard, isOpen]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (front.trim() && back.trim()) {\n      onSave(front.trim(), back.trim());\n      setFront('');\n      setBack('');\n      onClose();\n    }\n  };\n\n  const handleClose = () => {\n    setFront('');\n    setBack('');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={handleClose}>\n      <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">{title}</h2>\n          <button\n            onClick={handleClose}\n            className=\"close-button\"\n            type=\"button\"\n          >\n            ×\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"modal-form\">\n          <div className=\"form-group\">\n            <label htmlFor=\"front\" className=\"form-label\">\n              Front Side\n            </label>\n            <textarea\n              id=\"front\"\n              value={front}\n              onChange={(e) => setFront(e.target.value)}\n              placeholder=\"Enter the question or prompt...\"\n              className=\"form-textarea\"\n              rows={4}\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"back\" className=\"form-label\">\n              Back Side\n            </label>\n            <textarea\n              id=\"back\"\n              value={back}\n              onChange={(e) => setBack(e.target.value)}\n              placeholder=\"Enter the answer or explanation...\"\n              className=\"form-textarea\"\n              rows={4}\n              required\n            />\n          </div>\n\n          <div className=\"modal-actions\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              className=\"btn btn-secondary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={!front.trim() || !back.trim()}\n            >\n              {flashcard ? 'Update' : 'Create'} Flashcard\n            </button>\n          </div>\n        </form>\n      </div>\n\n      <style jsx>{`\n        .modal-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 20px;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 12px;\n          width: 100%;\n          max-width: 500px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 24px 24px 0 24px;\n          border-bottom: 1px solid #e5e7eb;\n          margin-bottom: 24px;\n        }\n\n        .modal-title {\n          font-size: 20px;\n          font-weight: 600;\n          color: #111827;\n          margin: 0;\n        }\n\n        .close-button {\n          background: none;\n          border: none;\n          font-size: 24px;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 4px;\n          border-radius: 4px;\n          transition: all 0.2s ease;\n        }\n\n        .close-button:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .modal-form {\n          padding: 0 24px 24px 24px;\n        }\n\n        .form-group {\n          margin-bottom: 20px;\n        }\n\n        .form-label {\n          display: block;\n          font-size: 14px;\n          font-weight: 500;\n          color: #374151;\n          margin-bottom: 8px;\n        }\n\n        .form-textarea {\n          width: 100%;\n          padding: 12px;\n          border: 2px solid #e5e7eb;\n          border-radius: 8px;\n          font-size: 14px;\n          line-height: 1.5;\n          color: #374151;\n          background: white;\n          transition: border-color 0.2s ease;\n          resize: vertical;\n          min-height: 80px;\n        }\n\n        .form-textarea:focus {\n          outline: none;\n          border-color: #3b82f6;\n          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n        }\n\n        .form-textarea::placeholder {\n          color: #9ca3af;\n        }\n\n        .modal-actions {\n          display: flex;\n          gap: 12px;\n          justify-content: flex-end;\n          margin-top: 24px;\n          padding-top: 20px;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .btn {\n          padding: 10px 20px;\n          border-radius: 8px;\n          font-size: 14px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          border: 2px solid transparent;\n        }\n\n        .btn:disabled {\n          opacity: 0.5;\n          cursor: not-allowed;\n        }\n\n        .btn-secondary {\n          background: white;\n          color: #374151;\n          border-color: #d1d5db;\n        }\n\n        .btn-secondary:hover:not(:disabled) {\n          background: #f9fafb;\n          border-color: #9ca3af;\n        }\n\n        .btn-primary {\n          background: #3b82f6;\n          color: white;\n          border-color: #3b82f6;\n        }\n\n        .btn-primary:hover:not(:disabled) {\n          background: #2563eb;\n          border-color: #2563eb;\n        }\n\n        @media (max-width: 640px) {\n          .modal-content {\n            margin: 10px;\n            max-width: none;\n          }\n\n          .modal-header {\n            padding: 20px 20px 0 20px;\n          }\n\n          .modal-form {\n            padding: 0 20px 20px 20px;\n          }\n\n          .modal-actions {\n            flex-direction: column;\n          }\n\n          .btn {\n            width: 100%;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAaO,MAAM,iBAAgD,CAAC,EAC5D,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,QAAQ,kBAAkB,EAC3B;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,SAAS,UAAU,KAAK;YACxB,QAAQ,UAAU,IAAI;QACxB,OAAO;YACL,SAAS;YACT,QAAQ;QACV;IACF,GAAG;QAAC;QAAW;KAAO;IAEtB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI;YAC/B,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI;YAC9B,SAAS;YACT,QAAQ;YACR;QACF;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,QAAQ;QACR;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAA8B,SAAS;iDAAzB;;0BACb,8OAAC;gBAA8B,SAAS,CAAC,IAAM,EAAE,eAAe;yDAAjD;;kCACb,8OAAC;iEAAc;;0CACb,8OAAC;yEAAa;0CAAe;;;;;;0CAC7B,8OAAC;gCACC,SAAS;gCAET,MAAK;yEADK;0CAEX;;;;;;;;;;;;kCAKH,8OAAC;wBAAK,UAAU;iEAAwB;;0CACtC,8OAAC;yEAAc;;kDACb,8OAAC;wCAAM,SAAQ;iFAAkB;kDAAa;;;;;;kDAG9C,8OAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCAEZ,MAAM;wCACN,QAAQ;iFAFE;;;;;;;;;;;;0CAMd,8OAAC;yEAAc;;kDACb,8OAAC;wCAAM,SAAQ;iFAAiB;kDAAa;;;;;;kDAG7C,8OAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACvC,aAAY;wCAEZ,MAAM;wCACN,QAAQ;iFAFE;;;;;;;;;;;;0CAMd,8OAAC;yEAAc;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;iFACC;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCAEL,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI;iFAD3B;;4CAGT,YAAY,WAAW;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8K/C", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/app/sets/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useParams, useRouter } from 'next/navigation';\nimport { useFlashcards } from '@/context/FlashcardContext';\nimport { FlashcardComponent } from '@/components/FlashcardComponent';\nimport { FlashcardModal } from '@/components/FlashcardModal';\nimport { Flashcard } from '@/types/flashcard';\n\nexport default function SetPage() {\n  const params = useParams();\n  const router = useRouter();\n  const { sets, currentSet, setCurrentSet, createFlashcard, updateFlashcard, deleteFlashcard } = useFlashcards();\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n  const [editingCard, setEditingCard] = useState<Flashcard | null>(null);\n\n  const setId = params.id as string;\n\n  useEffect(() => {\n    if (setId) {\n      setCurrentSet(setId);\n    }\n  }, [setId, setCurrentSet]);\n\n  useEffect(() => {\n    // If set doesn't exist, redirect to home\n    if (sets.length > 0 && !sets.find(set => set.id === setId)) {\n      router.push('/');\n    }\n  }, [sets, setId, router]);\n\n  const handleCreateCard = (front: string, back: string) => {\n    if (setId) {\n      createFlashcard(setId, front, back);\n    }\n  };\n\n  const handleEditCard = (card: Flashcard) => {\n    setEditingCard(card);\n  };\n\n  const handleUpdateCard = (front: string, back: string) => {\n    if (editingCard && setId) {\n      updateFlashcard(setId, editingCard.id, { front, back });\n      setEditingCard(null);\n    }\n  };\n\n  const handleDeleteCard = (cardId: string) => {\n    if (setId) {\n      deleteFlashcard(setId, cardId);\n    }\n  };\n\n  if (!currentSet) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">🔍</div>\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Set not found\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            The flashcard set you're looking for doesn't exist.\n          </p>\n          <Link\n            href=\"/\"\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n          >\n            Back to Home\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link\n                href=\"/\"\n                className=\"text-blue-600 hover:text-blue-700 mr-4\"\n              >\n                ← Back\n              </Link>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  {currentSet.name}\n                </h1>\n                {currentSet.description && (\n                  <p className=\"text-sm text-gray-600\">\n                    {currentSet.description}\n                  </p>\n                )}\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <Link\n                href={`/sets/${setId}/study`}\n                className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n              >\n                📖 Study Mode\n              </Link>\n              <button\n                onClick={() => setIsCreateModalOpen(true)}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n              >\n                + Add Card\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {currentSet.flashcards.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">📝</div>\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-2\">\n              No flashcards yet\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              Add your first flashcard to start building this set!\n            </p>\n            <button\n              onClick={() => setIsCreateModalOpen(true)}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\"\n            >\n              Add Your First Card\n            </button>\n          </div>\n        ) : (\n          <div>\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">\n                Flashcards ({currentSet.flashcards.length})\n              </h2>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {currentSet.flashcards.map((card) => (\n                <FlashcardComponent\n                  key={card.id}\n                  flashcard={card}\n                  onEdit={() => handleEditCard(card)}\n                  onDelete={() => handleDeleteCard(card.id)}\n                  showActions={true}\n                />\n              ))}\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Modals */}\n      <FlashcardModal\n        isOpen={isCreateModalOpen}\n        onClose={() => setIsCreateModalOpen(false)}\n        onSave={handleCreateCard}\n        title=\"Add New Flashcard\"\n      />\n\n      <FlashcardModal\n        isOpen={!!editingCard}\n        onClose={() => setEditingCard(null)}\n        onSave={handleUpdateCard}\n        flashcard={editingCard || undefined}\n        title=\"Edit Flashcard\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;IAC3G,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,MAAM,QAAQ,OAAO,EAAE;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,cAAc;QAChB;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,IAAI,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ;YAC1D,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAO;KAAO;IAExB,MAAM,mBAAmB,CAAC,OAAe;QACvC,IAAI,OAAO;YACT,gBAAgB,OAAO,OAAO;QAChC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC,OAAe;QACvC,IAAI,eAAe,OAAO;YACxB,gBAAgB,OAAO,YAAY,EAAE,EAAE;gBAAE;gBAAO;YAAK;YACrD,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO;YACT,gBAAgB,OAAO;QACzB;IACF;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,WAAW,IAAI;;;;;;4CAEjB,WAAW,WAAW,kBACrB,8OAAC;gDAAE,WAAU;0DACV,WAAW,WAAW;;;;;;;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC;wCAC5B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;0BACb,WAAW,UAAU,CAAC,MAAM,KAAK,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BACC,SAAS,IAAM,qBAAqB;4BACpC,WAAU;sCACX;;;;;;;;;;;yCAKH,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;oCAAsC;oCACrC,WAAW,UAAU,CAAC,MAAM;oCAAC;;;;;;;;;;;;sCAI9C,8OAAC;4BAAI,WAAU;sCACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC,wIAAA,CAAA,qBAAkB;oCAEjB,WAAW;oCACX,QAAQ,IAAM,eAAe;oCAC7B,UAAU,IAAM,iBAAiB,KAAK,EAAE;oCACxC,aAAa;mCAJR,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAaxB,8OAAC,oIAAA,CAAA,iBAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,QAAQ;gBACR,OAAM;;;;;;0BAGR,8OAAC,oIAAA,CAAA,iBAAc;gBACb,QAAQ,CAAC,CAAC;gBACV,SAAS,IAAM,eAAe;gBAC9B,QAAQ;gBACR,WAAW,eAAe;gBAC1B,OAAM;;;;;;;;;;;;AAId", "debugId": null}}]}