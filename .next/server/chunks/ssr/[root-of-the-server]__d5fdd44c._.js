module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/context/FlashcardContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FlashcardProvider": (()=>FlashcardProvider),
    "useFlashcards": (()=>useFlashcards)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
// Utility functions
const generateId = ()=>Math.random().toString(36).substr(2, 9);
const saveToLocalStorage = (sets)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
};
const loadFromLocalStorage = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return [];
};
// Reducer
const flashcardReducer = (state, action)=>{
    switch(action.type){
        case 'SET_SETS':
            return {
                ...state,
                sets: action.payload
            };
        case 'CREATE_SET':
            {
                const newSet = {
                    id: generateId(),
                    name: action.payload.name,
                    description: action.payload.description,
                    flashcards: [],
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                const newSets = [
                    ...state.sets,
                    newSet
                ];
                saveToLocalStorage(newSets);
                return {
                    ...state,
                    sets: newSets
                };
            }
        case 'UPDATE_SET':
            {
                const newSets = state.sets.map((set)=>set.id === action.payload.id ? {
                        ...set,
                        ...action.payload.updates,
                        updatedAt: new Date()
                    } : set);
                saveToLocalStorage(newSets);
                return {
                    ...state,
                    sets: newSets,
                    currentSet: state.currentSet?.id === action.payload.id ? newSets.find((set)=>set.id === action.payload.id) || null : state.currentSet
                };
            }
        case 'DELETE_SET':
            {
                const newSets = state.sets.filter((set)=>set.id !== action.payload);
                saveToLocalStorage(newSets);
                return {
                    ...state,
                    sets: newSets,
                    currentSet: state.currentSet?.id === action.payload ? null : state.currentSet
                };
            }
        case 'SET_CURRENT_SET':
            return {
                ...state,
                currentSet: action.payload ? state.sets.find((set)=>set.id === action.payload) || null : null
            };
        case 'CREATE_FLASHCARD':
            {
                const newCard = {
                    id: generateId(),
                    front: action.payload.front,
                    back: action.payload.back,
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                const newSets = state.sets.map((set)=>set.id === action.payload.setId ? {
                        ...set,
                        flashcards: [
                            ...set.flashcards,
                            newCard
                        ],
                        updatedAt: new Date()
                    } : set);
                saveToLocalStorage(newSets);
                return {
                    ...state,
                    sets: newSets,
                    currentSet: state.currentSet?.id === action.payload.setId ? newSets.find((set)=>set.id === action.payload.setId) || null : state.currentSet
                };
            }
        case 'UPDATE_FLASHCARD':
            {
                const newSets = state.sets.map((set)=>set.id === action.payload.setId ? {
                        ...set,
                        flashcards: set.flashcards.map((card)=>card.id === action.payload.cardId ? {
                                ...card,
                                ...action.payload.updates,
                                updatedAt: new Date()
                            } : card),
                        updatedAt: new Date()
                    } : set);
                saveToLocalStorage(newSets);
                return {
                    ...state,
                    sets: newSets,
                    currentSet: state.currentSet?.id === action.payload.setId ? newSets.find((set)=>set.id === action.payload.setId) || null : state.currentSet
                };
            }
        case 'DELETE_FLASHCARD':
            {
                const newSets = state.sets.map((set)=>set.id === action.payload.setId ? {
                        ...set,
                        flashcards: set.flashcards.filter((card)=>card.id !== action.payload.cardId),
                        updatedAt: new Date()
                    } : set);
                saveToLocalStorage(newSets);
                return {
                    ...state,
                    sets: newSets,
                    currentSet: state.currentSet?.id === action.payload.setId ? newSets.find((set)=>set.id === action.payload.setId) || null : state.currentSet
                };
            }
        default:
            return state;
    }
};
// Context
const FlashcardContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const FlashcardProvider = ({ children })=>{
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReducer"])(flashcardReducer, {
        sets: [],
        currentSet: null
    });
    // Load data from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const storedSets = loadFromLocalStorage();
        dispatch({
            type: 'SET_SETS',
            payload: storedSets
        });
    }, []);
    const contextValue = {
        sets: state.sets,
        currentSet: state.currentSet,
        createSet: (name, description)=>{
            dispatch({
                type: 'CREATE_SET',
                payload: {
                    name,
                    description
                }
            });
        },
        updateSet: (id, updates)=>{
            dispatch({
                type: 'UPDATE_SET',
                payload: {
                    id,
                    updates
                }
            });
        },
        deleteSet: (id)=>{
            dispatch({
                type: 'DELETE_SET',
                payload: id
            });
        },
        setCurrentSet: (setId)=>{
            dispatch({
                type: 'SET_CURRENT_SET',
                payload: setId
            });
        },
        createFlashcard: (setId, front, back)=>{
            dispatch({
                type: 'CREATE_FLASHCARD',
                payload: {
                    setId,
                    front,
                    back
                }
            });
        },
        updateFlashcard: (setId, cardId, updates)=>{
            dispatch({
                type: 'UPDATE_FLASHCARD',
                payload: {
                    setId,
                    cardId,
                    updates
                }
            });
        },
        deleteFlashcard: (setId, cardId)=>{
            dispatch({
                type: 'DELETE_FLASHCARD',
                payload: {
                    setId,
                    cardId
                }
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(FlashcardContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/context/FlashcardContext.tsx",
        lineNumber: 233,
        columnNumber: 5
    }, this);
};
const useFlashcards = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(FlashcardContext);
    if (context === undefined) {
        throw new Error('useFlashcards must be used within a FlashcardProvider');
    }
    return context;
};
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d5fdd44c._.js.map