{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/components/FlashcardComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Flashcard } from '@/types/flashcard';\n\ninterface FlashcardComponentProps {\n  flashcard: Flashcard;\n  onEdit?: () => void;\n  onDelete?: () => void;\n  showActions?: boolean;\n  className?: string;\n}\n\nexport const FlashcardComponent: React.FC<FlashcardComponentProps> = ({\n  flashcard,\n  onEdit,\n  onDelete,\n  showActions = true,\n  className = '',\n}) => {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const handleFlip = () => {\n    setIsFlipped(!isFlipped);\n  };\n\n  const handleEdit = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    onEdit?.();\n  };\n\n  const handleDelete = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    if (window.confirm('Are you sure you want to delete this flashcard?')) {\n      onDelete?.();\n    }\n  };\n\n  return (\n    <div className={`flashcard-container ${className}`}>\n      <div\n        className={`flashcard ${isFlipped ? 'flipped' : ''}`}\n        onClick={handleFlip}\n      >\n        <div className=\"flashcard-inner\">\n          {/* Front side */}\n          <div className=\"flashcard-front\">\n            <div className=\"flashcard-content\">\n              <div className=\"text-sm text-gray-500 mb-2\">Front</div>\n              <div className=\"flashcard-text\">\n                {flashcard.front || 'Click to add front content'}\n              </div>\n            </div>\n            {showActions && (\n              <div className=\"flashcard-actions\">\n                <button\n                  onClick={handleEdit}\n                  className=\"action-btn edit-btn\"\n                  title=\"Edit flashcard\"\n                >\n                  ✏️\n                </button>\n                <button\n                  onClick={handleDelete}\n                  className=\"action-btn delete-btn\"\n                  title=\"Delete flashcard\"\n                >\n                  🗑️\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Back side */}\n          <div className=\"flashcard-back\">\n            <div className=\"flashcard-content\">\n              <div className=\"text-sm text-gray-500 mb-2\">Back</div>\n              <div className=\"flashcard-text\">\n                {flashcard.back || 'Click to add back content'}\n              </div>\n            </div>\n            {showActions && (\n              <div className=\"flashcard-actions\">\n                <button\n                  onClick={handleEdit}\n                  className=\"action-btn edit-btn\"\n                  title=\"Edit flashcard\"\n                >\n                  ✏️\n                </button>\n                <button\n                  onClick={handleDelete}\n                  className=\"action-btn delete-btn\"\n                  title=\"Delete flashcard\"\n                >\n                  🗑️\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"flip-hint\">\n        Click to flip\n      </div>\n\n      <style jsx>{`\n        .flashcard-container {\n          perspective: 1000px;\n          width: 300px;\n          height: 200px;\n          margin: 10px;\n        }\n\n        .flashcard {\n          position: relative;\n          width: 100%;\n          height: 100%;\n          cursor: pointer;\n          transition: transform 0.6s;\n          transform-style: preserve-3d;\n        }\n\n        .flashcard.flipped {\n          transform: rotateY(180deg);\n        }\n\n        .flashcard-inner {\n          position: relative;\n          width: 100%;\n          height: 100%;\n        }\n\n        .flashcard-front,\n        .flashcard-back {\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          backface-visibility: hidden;\n          border-radius: 12px;\n          border: 2px solid #e5e7eb;\n          background: white;\n          display: flex;\n          flex-direction: column;\n          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n          transition: all 0.3s ease;\n        }\n\n        .flashcard-front:hover,\n        .flashcard-back:hover {\n          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\n          border-color: #3b82f6;\n        }\n\n        .flashcard-back {\n          transform: rotateY(180deg);\n          background: #f8fafc;\n        }\n\n        .flashcard-content {\n          flex: 1;\n          padding: 20px;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          text-align: center;\n        }\n\n        .flashcard-text {\n          font-size: 16px;\n          line-height: 1.5;\n          color: #374151;\n          word-wrap: break-word;\n          overflow-wrap: break-word;\n          max-height: 120px;\n          overflow-y: auto;\n        }\n\n        .flashcard-actions {\n          position: absolute;\n          top: 8px;\n          right: 8px;\n          display: flex;\n          gap: 4px;\n          opacity: 0;\n          transition: opacity 0.2s ease;\n        }\n\n        .flashcard-front:hover .flashcard-actions,\n        .flashcard-back:hover .flashcard-actions {\n          opacity: 1;\n        }\n\n        .action-btn {\n          width: 28px;\n          height: 28px;\n          border: none;\n          border-radius: 6px;\n          background: rgba(255, 255, 255, 0.9);\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          transition: all 0.2s ease;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        }\n\n        .action-btn:hover {\n          transform: scale(1.1);\n          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n        }\n\n        .edit-btn:hover {\n          background: #dbeafe;\n        }\n\n        .delete-btn:hover {\n          background: #fee2e2;\n        }\n\n        .flip-hint {\n          text-align: center;\n          margin-top: 8px;\n          font-size: 12px;\n          color: #6b7280;\n          opacity: 0.7;\n        }\n\n        @media (max-width: 640px) {\n          .flashcard-container {\n            width: 280px;\n            height: 180px;\n          }\n\n          .flashcard-content {\n            padding: 16px;\n          }\n\n          .flashcard-text {\n            font-size: 14px;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAaO,MAAM,qBAAwD,CAAC,EACpE,SAAS,EACT,MAAM,EACN,QAAQ,EACR,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB,aAAa,CAAC;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,IAAI,OAAO,OAAO,CAAC,oDAAoD;YACrE;QACF;IACF;IAEA,qBACE,8OAAC;kDAAe,CAAC,oBAAoB,EAAE,WAAW;;0BAChD,8OAAC;gBAEC,SAAS;0DADE,CAAC,UAAU,EAAE,YAAY,YAAY,IAAI;0BAGpD,cAAA,8OAAC;8DAAc;;sCAEb,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDAA6B;;;;;;sDAC5C,8OAAC;sFAAc;sDACZ,UAAU,KAAK,IAAI;;;;;;;;;;;;gCAGvB,6BACC,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDAA6B;;;;;;sDAC5C,8OAAC;sFAAc;sDACZ,UAAU,IAAI,IAAI;;;;;;;;;;;;gCAGtB,6BACC,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CAET,OAAM;sFADI;sDAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC;0DAAc;0BAAY;;;;;;;;;;;;;;;;AAiJjC", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/app/sets/%5Bid%5D/study/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useParams, useRouter } from 'next/navigation';\nimport { useFlashcards } from '@/context/FlashcardContext';\nimport { FlashcardComponent } from '@/components/FlashcardComponent';\n\nexport default function StudyPage() {\n  const params = useParams();\n  const router = useRouter();\n  const { sets, currentSet, setCurrentSet } = useFlashcards();\n  const [currentCardIndex, setCurrentCardIndex] = useState(0);\n  const [isShuffled, setIsShuffled] = useState(false);\n  const [shuffledCards, setShuffledCards] = useState<any[]>([]);\n\n  const setId = params.id as string;\n\n  useEffect(() => {\n    if (setId) {\n      setCurrentSet(setId);\n    }\n  }, [setId, setCurrentSet]);\n\n  useEffect(() => {\n    // If set doesn't exist, redirect to home\n    if (sets.length > 0 && !sets.find(set => set.id === setId)) {\n      router.push('/');\n    }\n  }, [sets, setId, router]);\n\n  useEffect(() => {\n    if (currentSet && currentSet.flashcards.length > 0) {\n      if (isShuffled) {\n        const shuffled = [...currentSet.flashcards].sort(() => Math.random() - 0.5);\n        setShuffledCards(shuffled);\n      } else {\n        setShuffledCards(currentSet.flashcards);\n      }\n      setCurrentCardIndex(0);\n    }\n  }, [currentSet, isShuffled]);\n\n  const cards = shuffledCards.length > 0 ? shuffledCards : currentSet?.flashcards || [];\n  const currentCard = cards[currentCardIndex];\n\n  const handleNext = () => {\n    if (currentCardIndex < cards.length - 1) {\n      setCurrentCardIndex(currentCardIndex + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentCardIndex > 0) {\n      setCurrentCardIndex(currentCardIndex - 1);\n    }\n  };\n\n  const handleShuffle = () => {\n    setIsShuffled(!isShuffled);\n  };\n\n  const handleRestart = () => {\n    setCurrentCardIndex(0);\n  };\n\n  if (!currentSet) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">🔍</div>\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Set not found\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            The flashcard set you're looking for doesn't exist.\n          </p>\n          <Link\n            href=\"/\"\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n          >\n            Back to Home\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  if (currentSet.flashcards.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-4xl mb-4\">📝</div>\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            No cards to study\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Add some flashcards to this set before studying.\n          </p>\n          <Link\n            href={`/sets/${setId}`}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n          >\n            Add Cards\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link\n                href={`/sets/${setId}`}\n                className=\"text-blue-600 hover:text-blue-700 mr-4\"\n              >\n                ← Back to Set\n              </Link>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  Study: {currentSet.name}\n                </h1>\n                <p className=\"text-sm text-gray-600\">\n                  Card {currentCardIndex + 1} of {cards.length}\n                </p>\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <button\n                onClick={handleShuffle}\n                className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n                  isShuffled\n                    ? 'bg-orange-600 hover:bg-orange-700 text-white'\n                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                }`}\n              >\n                🔀 {isShuffled ? 'Shuffled' : 'Shuffle'}\n              </button>\n              <button\n                onClick={handleRestart}\n                className=\"bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors\"\n              >\n                🔄 Restart\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Study Interface */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col items-center\">\n          {/* Progress Bar */}\n          <div className=\"w-full max-w-md mb-8\">\n            <div className=\"bg-gray-200 rounded-full h-2\">\n              <div\n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{\n                  width: `${((currentCardIndex + 1) / cards.length) * 100}%`,\n                }}\n              />\n            </div>\n            <div className=\"text-center text-sm text-gray-600 mt-2\">\n              Progress: {currentCardIndex + 1} / {cards.length}\n            </div>\n          </div>\n\n          {/* Flashcard */}\n          <div className=\"mb-8\">\n            <FlashcardComponent\n              flashcard={currentCard}\n              showActions={false}\n              className=\"scale-110\"\n            />\n          </div>\n\n          {/* Navigation Controls */}\n          <div className=\"flex gap-4\">\n            <button\n              onClick={handlePrevious}\n              disabled={currentCardIndex === 0}\n              className=\"bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors\"\n            >\n              ← Previous\n            </button>\n            \n            {currentCardIndex === cards.length - 1 ? (\n              <Link\n                href={`/sets/${setId}`}\n                className=\"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\"\n              >\n                ✅ Complete Study\n              </Link>\n            ) : (\n              <button\n                onClick={handleNext}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\"\n              >\n                Next →\n              </button>\n            )}\n          </div>\n\n          {/* Keyboard Shortcuts Hint */}\n          <div className=\"mt-8 text-center text-sm text-gray-500\">\n            <p>💡 Tip: Click the card to flip it</p>\n            <p className=\"mt-1\">Use arrow keys: ← Previous | → Next</p>\n          </div>\n        </div>\n      </main>\n\n      {/* Keyboard Navigation */}\n      <script\n        dangerouslySetInnerHTML={{\n          __html: `\n            document.addEventListener('keydown', function(e) {\n              if (e.key === 'ArrowLeft' && ${currentCardIndex} > 0) {\n                document.querySelector('[data-action=\"previous\"]')?.click();\n              } else if (e.key === 'ArrowRight' && ${currentCardIndex} < ${cards.length - 1}) {\n                document.querySelector('[data-action=\"next\"]')?.click();\n              }\n            });\n          `,\n        }}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;IACxD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE5D,MAAM,QAAQ,OAAO,EAAE;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,cAAc;QAChB;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,IAAI,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ;YAC1D,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAO;KAAO;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,WAAW,UAAU,CAAC,MAAM,GAAG,GAAG;YAClD,IAAI,YAAY;gBACd,MAAM,WAAW;uBAAI,WAAW,UAAU;iBAAC,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;gBACvE,iBAAiB;YACnB,OAAO;gBACL,iBAAiB,WAAW,UAAU;YACxC;YACA,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAY;KAAW;IAE3B,MAAM,QAAQ,cAAc,MAAM,GAAG,IAAI,gBAAgB,YAAY,cAAc,EAAE;IACrF,MAAM,cAAc,KAAK,CAAC,iBAAiB;IAE3C,MAAM,aAAa;QACjB,IAAI,mBAAmB,MAAM,MAAM,GAAG,GAAG;YACvC,oBAAoB,mBAAmB;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,mBAAmB,GAAG;YACxB,oBAAoB,mBAAmB;QACzC;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc,CAAC;IACjB;IAEA,MAAM,gBAAgB;QACpB,oBAAoB;IACtB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,WAAW,UAAU,CAAC,MAAM,KAAK,GAAG;QACtC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,MAAM,EAAE,OAAO;wBACtB,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,MAAM,EAAE,OAAO;wCACtB,WAAU;kDACX;;;;;;kDAGD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAkC;oDACtC,WAAW,IAAI;;;;;;;0DAEzB,8OAAC;gDAAE,WAAU;;oDAAwB;oDAC7B,mBAAmB;oDAAE;oDAAK,MAAM,MAAM;;;;;;;;;;;;;;;;;;;0CAIlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,mDAAmD,EAC7D,aACI,iDACA,+CACJ;;4CACH;4CACK,aAAa,aAAa;;;;;;;kDAEhC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,GAAG,AAAC,CAAC,mBAAmB,CAAC,IAAI,MAAM,MAAM,GAAI,IAAI,CAAC,CAAC;wCAC5D;;;;;;;;;;;8CAGJ,8OAAC;oCAAI,WAAU;;wCAAyC;wCAC3C,mBAAmB;wCAAE;wCAAI,MAAM,MAAM;;;;;;;;;;;;;sCAKpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAA,CAAA,qBAAkB;gCACjB,WAAW;gCACX,aAAa;gCACb,WAAU;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU,qBAAqB;oCAC/B,WAAU;8CACX;;;;;;gCAIA,qBAAqB,MAAM,MAAM,GAAG,kBACnC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,MAAM,EAAE,OAAO;oCACtB,WAAU;8CACX;;;;;yDAID,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAO;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBACC,yBAAyB;oBACvB,QAAQ,CAAC;;2CAEwB,EAAE,iBAAiB;;mDAEX,EAAE,iBAAiB,GAAG,EAAE,MAAM,MAAM,GAAG,EAAE;;;;UAIlF,CAAC;gBACH;;;;;;;;;;;;AAIR", "debugId": null}}]}