{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/components/SetModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { FlashcardSet } from '@/types/flashcard';\n\ninterface SetModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (name: string, description?: string) => void;\n  set?: FlashcardSet;\n  title?: string;\n}\n\nexport const SetModal: React.FC<SetModalProps> = ({\n  isOpen,\n  onClose,\n  onSave,\n  set,\n  title = 'Create Flashcard Set',\n}) => {\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n\n  useEffect(() => {\n    if (set) {\n      setName(set.name);\n      setDescription(set.description || '');\n    } else {\n      setName('');\n      setDescription('');\n    }\n  }, [set, isOpen]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (name.trim()) {\n      onSave(name.trim(), description.trim() || undefined);\n      setName('');\n      setDescription('');\n      onClose();\n    }\n  };\n\n  const handleClose = () => {\n    setName('');\n    setDescription('');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={handleClose}>\n      <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">{title}</h2>\n          <button\n            onClick={handleClose}\n            className=\"close-button\"\n            type=\"button\"\n          >\n            ×\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"modal-form\">\n          <div className=\"form-group\">\n            <label htmlFor=\"name\" className=\"form-label\">\n              Set Name *\n            </label>\n            <input\n              id=\"name\"\n              type=\"text\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              placeholder=\"Enter set name...\"\n              className=\"form-input\"\n              required\n              maxLength={100}\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"description\" className=\"form-label\">\n              Description (Optional)\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"Enter a description for this set...\"\n              className=\"form-textarea\"\n              rows={3}\n              maxLength={500}\n            />\n          </div>\n\n          <div className=\"modal-actions\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              className=\"btn btn-secondary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={!name.trim()}\n            >\n              {set ? 'Update' : 'Create'} Set\n            </button>\n          </div>\n        </form>\n      </div>\n\n      <style jsx>{`\n        .modal-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n          padding: 20px;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 12px;\n          width: 100%;\n          max-width: 450px;\n          max-height: 90vh;\n          overflow-y: auto;\n          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 24px 24px 0 24px;\n          border-bottom: 1px solid #e5e7eb;\n          margin-bottom: 24px;\n        }\n\n        .modal-title {\n          font-size: 20px;\n          font-weight: 600;\n          color: #111827;\n          margin: 0;\n        }\n\n        .close-button {\n          background: none;\n          border: none;\n          font-size: 24px;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 4px;\n          border-radius: 4px;\n          transition: all 0.2s ease;\n        }\n\n        .close-button:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .modal-form {\n          padding: 0 24px 24px 24px;\n        }\n\n        .form-group {\n          margin-bottom: 20px;\n        }\n\n        .form-label {\n          display: block;\n          font-size: 14px;\n          font-weight: 500;\n          color: #374151;\n          margin-bottom: 8px;\n        }\n\n        .form-input,\n        .form-textarea {\n          width: 100%;\n          padding: 12px;\n          border: 2px solid #e5e7eb;\n          border-radius: 8px;\n          font-size: 14px;\n          line-height: 1.5;\n          color: #374151;\n          background: white;\n          transition: border-color 0.2s ease;\n        }\n\n        .form-textarea {\n          resize: vertical;\n          min-height: 60px;\n        }\n\n        .form-input:focus,\n        .form-textarea:focus {\n          outline: none;\n          border-color: #3b82f6;\n          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n        }\n\n        .form-input::placeholder,\n        .form-textarea::placeholder {\n          color: #9ca3af;\n        }\n\n        .modal-actions {\n          display: flex;\n          gap: 12px;\n          justify-content: flex-end;\n          margin-top: 24px;\n          padding-top: 20px;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .btn {\n          padding: 10px 20px;\n          border-radius: 8px;\n          font-size: 14px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          border: 2px solid transparent;\n        }\n\n        .btn:disabled {\n          opacity: 0.5;\n          cursor: not-allowed;\n        }\n\n        .btn-secondary {\n          background: white;\n          color: #374151;\n          border-color: #d1d5db;\n        }\n\n        .btn-secondary:hover:not(:disabled) {\n          background: #f9fafb;\n          border-color: #9ca3af;\n        }\n\n        .btn-primary {\n          background: #3b82f6;\n          color: white;\n          border-color: #3b82f6;\n        }\n\n        .btn-primary:hover:not(:disabled) {\n          background: #2563eb;\n          border-color: #2563eb;\n        }\n\n        @media (max-width: 640px) {\n          .modal-content {\n            margin: 10px;\n            max-width: none;\n          }\n\n          .modal-header {\n            padding: 20px 20px 0 20px;\n          }\n\n          .modal-form {\n            padding: 0 20px 20px 20px;\n          }\n\n          .modal-actions {\n            flex-direction: column;\n          }\n\n          .btn {\n            width: 100%;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAaO,MAAM,WAAoC,CAAC,EAChD,MAAM,EACN,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,sBAAsB,EAC/B;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK;YACP,QAAQ,IAAI,IAAI;YAChB,eAAe,IAAI,WAAW,IAAI;QACpC,OAAO;YACL,QAAQ;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAK;KAAO;IAEhB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,KAAK,IAAI,IAAI;YACf,OAAO,KAAK,IAAI,IAAI,YAAY,IAAI,MAAM;YAC1C,QAAQ;YACR,eAAe;YACf;QACF;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,eAAe;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAA8B,SAAS;kDAAzB;;0BACb,8OAAC;gBAA8B,SAAS,CAAC,IAAM,EAAE,eAAe;0DAAjD;;kCACb,8OAAC;kEAAc;;0CACb,8OAAC;0EAAa;0CAAe;;;;;;0CAC7B,8OAAC;gCACC,SAAS;gCAET,MAAK;0EADK;0CAEX;;;;;;;;;;;;kCAKH,8OAAC;wBAAK,UAAU;kEAAwB;;0CACtC,8OAAC;0EAAc;;kDACb,8OAAC;wCAAM,SAAQ;kFAAiB;kDAAa;;;;;;kDAG7C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACvC,aAAY;wCAEZ,QAAQ;wCACR,WAAW;kFAFD;;;;;;;;;;;;0CAMd,8OAAC;0EAAc;;kDACb,8OAAC;wCAAM,SAAQ;kFAAwB;kDAAa;;;;;;kDAGpD,8OAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCAEZ,MAAM;wCACN,WAAW;kFAFD;;;;;;;;;;;;0CAMd,8OAAC;0EAAc;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;kFACC;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCAEL,UAAU,CAAC,KAAK,IAAI;kFADV;;4CAGT,MAAM,WAAW;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoLzC", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/hooks/useSampleData.ts"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\nimport { useFlashcards } from \"@/context/FlashcardContext\";\n\nexport const useSampleData = () => {\n  const { createSet, createFlashcard } = useFlashcards();\n  const hasInitialized = useRef(false);\n\n  useEffect(() => {\n    // Only run once and only if no data exists in localStorage\n    if (hasInitialized.current) {\n      return;\n    }\n\n    // Check if data already exists in localStorage\n    const existingData = localStorage.getItem(\"flashcard-sets\");\n    if (existingData && JSON.parse(existingData).length > 0) {\n      hasInitialized.current = true;\n      return;\n    }\n\n    hasInitialized.current = true;\n\n    // Create sample data\n    const sampleData = [\n      {\n        name: \"Spanish Vocabulary\",\n        description: \"Basic Spanish words and phrases for beginners\",\n        cards: [\n          { front: \"Hello\", back: \"Hola\" },\n          { front: \"Goodbye\", back: \"Adiós\" },\n          { front: \"Thank you\", back: \"Gracias\" },\n          { front: \"Please\", back: \"Por favor\" },\n          { front: \"Yes\", back: \"Sí\" },\n          { front: \"No\", back: \"No\" },\n          { front: \"Water\", back: \"Agua\" },\n          { front: \"Food\", back: \"Comida\" },\n          { front: \"House\", back: \"Casa\" },\n          { front: \"Friend\", back: \"Amigo/Amiga\" },\n        ],\n      },\n      {\n        name: \"JavaScript Fundamentals\",\n        description: \"Core JavaScript concepts every developer should know\",\n        cards: [\n          {\n            front: \"What is a closure in JavaScript?\",\n            back: \"A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.\",\n          },\n          {\n            front: \"What is the difference between let, const, and var?\",\n            back: \"var is function-scoped and can be redeclared. let is block-scoped and can be reassigned. const is block-scoped and cannot be reassigned.\",\n          },\n          {\n            front: \"What is event bubbling?\",\n            back: \"Event bubbling is when an event starts from the target element and bubbles up to the parent elements in the DOM hierarchy.\",\n          },\n          {\n            front: \"What is the difference between == and ===?\",\n            back: \"== performs type coercion and compares values. === compares both value and type without coercion.\",\n          },\n          {\n            front: \"What is a Promise?\",\n            back: \"A Promise is an object representing the eventual completion or failure of an asynchronous operation.\",\n          },\n          {\n            front: \"What is the purpose of async/await?\",\n            back: \"async/await provides a way to write asynchronous code that looks and behaves more like synchronous code.\",\n          },\n        ],\n      },\n      {\n        name: \"World History\",\n        description: \"Important historical events and dates\",\n        cards: [\n          {\n            front: \"When did World War II end?\",\n            back: \"September 2, 1945 (with Japan's surrender)\",\n          },\n          {\n            front: \"Who was the first person to walk on the moon?\",\n            back: \"Neil Armstrong on July 20, 1969\",\n          },\n          {\n            front: \"When did the Berlin Wall fall?\",\n            back: \"November 9, 1989\",\n          },\n          {\n            front: \"What year did the American Civil War begin?\",\n            back: \"1861\",\n          },\n          {\n            front: \"Who wrote the Declaration of Independence?\",\n            back: \"Thomas Jefferson (primary author)\",\n          },\n          {\n            front: \"When did the French Revolution begin?\",\n            back: \"1789\",\n          },\n        ],\n      },\n    ];\n\n    // Create sets and cards sequentially to avoid race conditions\n    const createSampleData = async () => {\n      for (const setData of sampleData) {\n        createSet(setData.name, setData.description);\n\n        // Small delay to ensure set is created\n        await new Promise((resolve) => setTimeout(resolve, 50));\n\n        // Get the created set ID from localStorage\n        const currentSets = JSON.parse(\n          localStorage.getItem(\"flashcard-sets\") ?? \"[]\"\n        );\n        const createdSet = currentSets.find(\n          (set: any) => set.name === setData.name\n        );\n\n        if (createdSet) {\n          for (const card of setData.cards) {\n            createFlashcard(createdSet.id, card.front, card.back);\n            // Small delay between card creation\n            await new Promise((resolve) => setTimeout(resolve, 10));\n          }\n        }\n      }\n    };\n\n    createSampleData();\n  }, []); // Empty dependency array - only run once\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2DAA2D;QAC3D,IAAI,eAAe,OAAO,EAAE;YAC1B;QACF;QAEA,+CAA+C;QAC/C,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,gBAAgB,KAAK,KAAK,CAAC,cAAc,MAAM,GAAG,GAAG;YACvD,eAAe,OAAO,GAAG;YACzB;QACF;QAEA,eAAe,OAAO,GAAG;QAEzB,qBAAqB;QACrB,MAAM,aAAa;YACjB;gBACE,MAAM;gBACN,aAAa;gBACb,OAAO;oBACL;wBAAE,OAAO;wBAAS,MAAM;oBAAO;oBAC/B;wBAAE,OAAO;wBAAW,MAAM;oBAAQ;oBAClC;wBAAE,OAAO;wBAAa,MAAM;oBAAU;oBACtC;wBAAE,OAAO;wBAAU,MAAM;oBAAY;oBACrC;wBAAE,OAAO;wBAAO,MAAM;oBAAK;oBAC3B;wBAAE,OAAO;wBAAM,MAAM;oBAAK;oBAC1B;wBAAE,OAAO;wBAAS,MAAM;oBAAO;oBAC/B;wBAAE,OAAO;wBAAQ,MAAM;oBAAS;oBAChC;wBAAE,OAAO;wBAAS,MAAM;oBAAO;oBAC/B;wBAAE,OAAO;wBAAU,MAAM;oBAAc;iBACxC;YACH;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,OAAO;oBACL;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;iBACD;YACH;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,OAAO;oBACL;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,MAAM;oBACR;iBACD;YACH;SACD;QAED,8DAA8D;QAC9D,MAAM,mBAAmB;YACvB,KAAK,MAAM,WAAW,WAAY;gBAChC,UAAU,QAAQ,IAAI,EAAE,QAAQ,WAAW;gBAE3C,uCAAuC;gBACvC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBAEnD,2CAA2C;gBAC3C,MAAM,cAAc,KAAK,KAAK,CAC5B,aAAa,OAAO,CAAC,qBAAqB;gBAE5C,MAAM,aAAa,YAAY,IAAI,CACjC,CAAC,MAAa,IAAI,IAAI,KAAK,QAAQ,IAAI;gBAGzC,IAAI,YAAY;oBACd,KAAK,MAAM,QAAQ,QAAQ,KAAK,CAAE;wBAChC,gBAAgB,WAAW,EAAE,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI;wBACpD,oCAAoC;wBACpC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;oBACrD;gBACF;YACF;QACF;QAEA;IACF,GAAG,EAAE,GAAG,yCAAyC;AACnD", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useFlashcards } from \"@/context/FlashcardContext\";\nimport { SetModal } from \"@/components/SetModal\";\nimport { useSampleData } from \"@/hooks/useSampleData\";\n\nexport default function Home() {\n  const { sets, createSet, updateSet, deleteSet } = useFlashcards();\n  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);\n  const [editingSet, setEditingSet] = useState(null);\n\n  // Initialize sample data for demonstration\n  useSampleData();\n\n  const handleCreateSet = (name: string, description?: string) => {\n    createSet(name, description);\n  };\n\n  const handleEditSet = (set: any) => {\n    setEditingSet(set);\n  };\n\n  const handleUpdateSet = (name: string, description?: string) => {\n    if (editingSet) {\n      updateSet(editingSet.id, { name, description });\n      setEditingSet(null);\n    }\n  };\n\n  const handleDeleteSet = (setId: string) => {\n    if (\n      window.confirm(\n        \"Are you sure you want to delete this set? All flashcards in this set will be lost.\"\n      )\n    ) {\n      deleteSet(setId);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">📚 Flashy</h1>\n              <span className=\"ml-2 text-sm text-gray-500\">\n                Flashcard Learning App\n              </span>\n            </div>\n            <button\n              onClick={() => setIsCreateModalOpen(true)}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n            >\n              + New Set\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {sets.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">📚</div>\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-2\">\n              No flashcard sets yet\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              Create your first flashcard set to start learning!\n            </p>\n            <button\n              onClick={() => setIsCreateModalOpen(true)}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\"\n            >\n              Create Your First Set\n            </button>\n          </div>\n        ) : (\n          <div>\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">\n                Your Flashcard Sets ({sets.length})\n              </h2>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {sets.map((set) => (\n                <div\n                  key={set.id}\n                  className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow\"\n                >\n                  <div className=\"p-6\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n                        {set.name}\n                      </h3>\n                      <div className=\"flex gap-1 ml-2\">\n                        <button\n                          onClick={() => handleEditSet(set)}\n                          className=\"text-gray-400 hover:text-blue-600 p-1\"\n                          title=\"Edit set\"\n                        >\n                          ✏️\n                        </button>\n                        <button\n                          onClick={() => handleDeleteSet(set.id)}\n                          className=\"text-gray-400 hover:text-red-600 p-1\"\n                          title=\"Delete set\"\n                        >\n                          🗑️\n                        </button>\n                      </div>\n                    </div>\n\n                    {set.description && (\n                      <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n                        {set.description}\n                      </p>\n                    )}\n\n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      <span>{set.flashcards.length} cards</span>\n                      <span>\n                        Updated {new Date(set.updatedAt).toLocaleDateString()}\n                      </span>\n                    </div>\n\n                    <Link\n                      href={`/sets/${set.id}`}\n                      className=\"block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors\"\n                    >\n                      Study Set\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Modals */}\n      <SetModal\n        isOpen={isCreateModalOpen}\n        onClose={() => setIsCreateModalOpen(false)}\n        onSave={handleCreateSet}\n        title=\"Create New Flashcard Set\"\n      />\n\n      <SetModal\n        isOpen={!!editingSet}\n        onClose={() => setEditingSet(null)}\n        onSave={handleUpdateSet}\n        set={editingSet}\n        title=\"Edit Flashcard Set\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,2CAA2C;IAC3C,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAEZ,MAAM,kBAAkB,CAAC,MAAc;QACrC,UAAU,MAAM;IAClB;IAEA,MAAM,gBAAgB,CAAC;QACrB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC,MAAc;QACrC,IAAI,YAAY;YACd,UAAU,WAAW,EAAE,EAAE;gBAAE;gBAAM;YAAY;YAC7C,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IACE,OAAO,OAAO,CACZ,uFAEF;YACA,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;0CAI/C,8OAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAK,WAAU;0BACb,KAAK,MAAM,KAAK,kBACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BACC,SAAS,IAAM,qBAAqB;4BACpC,WAAU;sCACX;;;;;;;;;;;yCAKH,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;oCAAsC;oCAC5B,KAAK,MAAM;oCAAC;;;;;;;;;;;;sCAItC,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,IAAI,IAAI;;;;;;kEAEX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,cAAc;gEAC7B,WAAU;gEACV,OAAM;0EACP;;;;;;0EAGD,8OAAC;gEACC,SAAS,IAAM,gBAAgB,IAAI,EAAE;gEACrC,WAAU;gEACV,OAAM;0EACP;;;;;;;;;;;;;;;;;;4CAMJ,IAAI,WAAW,kBACd,8OAAC;gDAAE,WAAU;0DACV,IAAI,WAAW;;;;;;0DAIpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAM,IAAI,UAAU,CAAC,MAAM;4DAAC;;;;;;;kEAC7B,8OAAC;;4DAAK;4DACK,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;0DAIvD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gDACvB,WAAU;0DACX;;;;;;;;;;;;mCA1CE,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;0BAsDvB,8OAAC,8HAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,QAAQ;gBACR,OAAM;;;;;;0BAGR,8OAAC,8HAAA,CAAA,WAAQ;gBACP,QAAQ,CAAC,CAAC;gBACV,SAAS,IAAM,cAAc;gBAC7B,QAAQ;gBACR,KAAK;gBACL,OAAM;;;;;;;;;;;;AAId", "debugId": null}}]}