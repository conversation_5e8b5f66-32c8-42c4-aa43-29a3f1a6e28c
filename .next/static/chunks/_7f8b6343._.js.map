{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/src/context/FlashcardContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { FlashcardContextType, FlashcardSet, Flashcard } from '@/types/flashcard';\n\n// Action types\ntype FlashcardAction =\n  | { type: 'SET_SETS'; payload: FlashcardSet[] }\n  | { type: 'CREATE_SET'; payload: { name: string; description?: string } }\n  | { type: 'UPDATE_SET'; payload: { id: string; updates: Partial<Omit<FlashcardSet, 'id' | 'flashcards' | 'createdAt'>> } }\n  | { type: 'DELETE_SET'; payload: string }\n  | { type: 'SET_CURRENT_SET'; payload: string | null }\n  | { type: 'CREATE_FLASHCARD'; payload: { setId: string; front: string; back: string } }\n  | { type: 'UPDATE_FLASHCARD'; payload: { setId: string; cardId: string; updates: Partial<Omit<Flashcard, 'id' | 'createdAt'>> } }\n  | { type: 'DELETE_FLASHCARD'; payload: { setId: string; cardId: string } };\n\ninterface FlashcardState {\n  sets: FlashcardSet[];\n  currentSet: FlashcardSet | null;\n}\n\n// Utility functions\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\nconst saveToLocalStorage = (sets: FlashcardSet[]) => {\n  if (typeof window !== 'undefined') {\n    localStorage.setItem('flashcard-sets', JSON.stringify(sets));\n  }\n};\n\nconst loadFromLocalStorage = (): FlashcardSet[] => {\n  if (typeof window !== 'undefined') {\n    const stored = localStorage.getItem('flashcard-sets');\n    if (stored) {\n      try {\n        const parsed = JSON.parse(stored);\n        return parsed.map((set: any) => ({\n          ...set,\n          createdAt: new Date(set.createdAt),\n          updatedAt: new Date(set.updatedAt),\n          flashcards: set.flashcards.map((card: any) => ({\n            ...card,\n            createdAt: new Date(card.createdAt),\n            updatedAt: new Date(card.updatedAt),\n          })),\n        }));\n      } catch (error) {\n        console.error('Error parsing stored flashcard sets:', error);\n      }\n    }\n  }\n  return [];\n};\n\n// Reducer\nconst flashcardReducer = (state: FlashcardState, action: FlashcardAction): FlashcardState => {\n  switch (action.type) {\n    case 'SET_SETS':\n      return {\n        ...state,\n        sets: action.payload,\n      };\n\n    case 'CREATE_SET': {\n      const newSet: FlashcardSet = {\n        id: generateId(),\n        name: action.payload.name,\n        description: action.payload.description,\n        flashcards: [],\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n      const newSets = [...state.sets, newSet];\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n      };\n    }\n\n    case 'UPDATE_SET': {\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.id\n          ? { ...set, ...action.payload.updates, updatedAt: new Date() }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.id\n          ? newSets.find(set => set.id === action.payload.id) || null\n          : state.currentSet,\n      };\n    }\n\n    case 'DELETE_SET': {\n      const newSets = state.sets.filter(set => set.id !== action.payload);\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload ? null : state.currentSet,\n      };\n    }\n\n    case 'SET_CURRENT_SET':\n      return {\n        ...state,\n        currentSet: action.payload ? state.sets.find(set => set.id === action.payload) || null : null,\n      };\n\n    case 'CREATE_FLASHCARD': {\n      const newCard: Flashcard = {\n        id: generateId(),\n        front: action.payload.front,\n        back: action.payload.back,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.setId\n          ? {\n              ...set,\n              flashcards: [...set.flashcards, newCard],\n              updatedAt: new Date(),\n            }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.setId\n          ? newSets.find(set => set.id === action.payload.setId) || null\n          : state.currentSet,\n      };\n    }\n\n    case 'UPDATE_FLASHCARD': {\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.setId\n          ? {\n              ...set,\n              flashcards: set.flashcards.map(card =>\n                card.id === action.payload.cardId\n                  ? { ...card, ...action.payload.updates, updatedAt: new Date() }\n                  : card\n              ),\n              updatedAt: new Date(),\n            }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.setId\n          ? newSets.find(set => set.id === action.payload.setId) || null\n          : state.currentSet,\n      };\n    }\n\n    case 'DELETE_FLASHCARD': {\n      const newSets = state.sets.map(set =>\n        set.id === action.payload.setId\n          ? {\n              ...set,\n              flashcards: set.flashcards.filter(card => card.id !== action.payload.cardId),\n              updatedAt: new Date(),\n            }\n          : set\n      );\n      saveToLocalStorage(newSets);\n      return {\n        ...state,\n        sets: newSets,\n        currentSet: state.currentSet?.id === action.payload.setId\n          ? newSets.find(set => set.id === action.payload.setId) || null\n          : state.currentSet,\n      };\n    }\n\n    default:\n      return state;\n  }\n};\n\n// Context\nconst FlashcardContext = createContext<FlashcardContextType | undefined>(undefined);\n\n// Provider component\nexport const FlashcardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(flashcardReducer, {\n    sets: [],\n    currentSet: null,\n  });\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const storedSets = loadFromLocalStorage();\n    dispatch({ type: 'SET_SETS', payload: storedSets });\n  }, []);\n\n  const contextValue: FlashcardContextType = {\n    sets: state.sets,\n    currentSet: state.currentSet,\n    createSet: (name: string, description?: string) => {\n      dispatch({ type: 'CREATE_SET', payload: { name, description } });\n    },\n    updateSet: (id: string, updates) => {\n      dispatch({ type: 'UPDATE_SET', payload: { id, updates } });\n    },\n    deleteSet: (id: string) => {\n      dispatch({ type: 'DELETE_SET', payload: id });\n    },\n    setCurrentSet: (setId: string | null) => {\n      dispatch({ type: 'SET_CURRENT_SET', payload: setId });\n    },\n    createFlashcard: (setId: string, front: string, back: string) => {\n      dispatch({ type: 'CREATE_FLASHCARD', payload: { setId, front, back } });\n    },\n    updateFlashcard: (setId: string, cardId: string, updates) => {\n      dispatch({ type: 'UPDATE_FLASHCARD', payload: { setId, cardId, updates } });\n    },\n    deleteFlashcard: (setId: string, cardId: string) => {\n      dispatch({ type: 'DELETE_FLASHCARD', payload: { setId, cardId } });\n    },\n  };\n\n  return (\n    <FlashcardContext.Provider value={contextValue}>\n      {children}\n    </FlashcardContext.Provider>\n  );\n};\n\n// Hook to use the context\nexport const useFlashcards = () => {\n  const context = useContext(FlashcardContext);\n  if (context === undefined) {\n    throw new Error('useFlashcards must be used within a FlashcardProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAqBA,oBAAoB;AACpB,MAAM,aAAa,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAE9D,MAAM,qBAAqB,CAAC;IAC1B,wCAAmC;QACjC,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;IACxD;AACF;AAEA,MAAM,uBAAuB;IAC3B,wCAAmC;QACjC,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,OAAO,OAAO,GAAG,CAAC,CAAC,MAAa,CAAC;wBAC/B,GAAG,GAAG;wBACN,WAAW,IAAI,KAAK,IAAI,SAAS;wBACjC,WAAW,IAAI,KAAK,IAAI,SAAS;wBACjC,YAAY,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;gCAC7C,GAAG,IAAI;gCACP,WAAW,IAAI,KAAK,KAAK,SAAS;gCAClC,WAAW,IAAI,KAAK,KAAK,SAAS;4BACpC,CAAC;oBACH,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD;QACF;IACF;IACA,OAAO,EAAE;AACX;AAEA,UAAU;AACV,MAAM,mBAAmB,CAAC,OAAuB;IAC/C,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO;YACtB;QAEF,KAAK;YAAc;gBACjB,MAAM,SAAuB;oBAC3B,IAAI;oBACJ,MAAM,OAAO,OAAO,CAAC,IAAI;oBACzB,aAAa,OAAO,OAAO,CAAC,WAAW;oBACvC,YAAY,EAAE;oBACd,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBACA,MAAM,UAAU;uBAAI,MAAM,IAAI;oBAAE;iBAAO;gBACvC,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;gBACR;YACF;QAEA,KAAK;YAAc;gBACjB,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACxB;wBAAE,GAAG,GAAG;wBAAE,GAAG,OAAO,OAAO,CAAC,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC3D;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,EAAE,GAClD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,OACrD,MAAM,UAAU;gBACtB;YACF;QAEA,KAAK;YAAc;gBACjB,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO;gBAClE,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,GAAG,OAAO,MAAM,UAAU;gBAC/E;YACF;QAEA,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY,OAAO,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,KAAK,OAAO;YAC3F;QAEF,KAAK;YAAoB;gBACvB,MAAM,UAAqB;oBACzB,IAAI;oBACJ,OAAO,OAAO,OAAO,CAAC,KAAK;oBAC3B,MAAM,OAAO,OAAO,CAAC,IAAI;oBACzB,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBAEA,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,GAC3B;wBACE,GAAG,GAAG;wBACN,YAAY;+BAAI,IAAI,UAAU;4BAAE;yBAAQ;wBACxC,WAAW,IAAI;oBACjB,IACA;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,KAAK,GACrD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,OACxD,MAAM,UAAU;gBACtB;YACF;QAEA,KAAK;YAAoB;gBACvB,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,GAC3B;wBACE,GAAG,GAAG;wBACN,YAAY,IAAI,UAAU,CAAC,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,MAAM,GAC7B;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO,OAAO,CAAC,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAC5D;wBAEN,WAAW,IAAI;oBACjB,IACA;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,KAAK,GACrD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,OACxD,MAAM,UAAU;gBACtB;YACF;QAEA,KAAK;YAAoB;gBACvB,MAAM,UAAU,MAAM,IAAI,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,GAC3B;wBACE,GAAG,GAAG;wBACN,YAAY,IAAI,UAAU,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,MAAM;wBAC3E,WAAW,IAAI;oBACjB,IACA;gBAEN,mBAAmB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;oBACN,YAAY,MAAM,UAAU,EAAE,OAAO,OAAO,OAAO,CAAC,KAAK,GACrD,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,OACxD,MAAM,UAAU;gBACtB;YACF;QAEA;YACE,OAAO;IACX;AACF;AAEA,UAAU;AACV,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAoC;AAGlE,MAAM,oBAA6D,CAAC,EAAE,QAAQ,EAAE;;IACrF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;QACrD,MAAM,EAAE;QACR,YAAY;IACd;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,aAAa;YACnB,SAAS;gBAAE,MAAM;gBAAY,SAAS;YAAW;QACnD;sCAAG,EAAE;IAEL,MAAM,eAAqC;QACzC,MAAM,MAAM,IAAI;QAChB,YAAY,MAAM,UAAU;QAC5B,WAAW,CAAC,MAAc;YACxB,SAAS;gBAAE,MAAM;gBAAc,SAAS;oBAAE;oBAAM;gBAAY;YAAE;QAChE;QACA,WAAW,CAAC,IAAY;YACtB,SAAS;gBAAE,MAAM;gBAAc,SAAS;oBAAE;oBAAI;gBAAQ;YAAE;QAC1D;QACA,WAAW,CAAC;YACV,SAAS;gBAAE,MAAM;gBAAc,SAAS;YAAG;QAC7C;QACA,eAAe,CAAC;YACd,SAAS;gBAAE,MAAM;gBAAmB,SAAS;YAAM;QACrD;QACA,iBAAiB,CAAC,OAAe,OAAe;YAC9C,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAO;oBAAO;gBAAK;YAAE;QACvE;QACA,iBAAiB,CAAC,OAAe,QAAgB;YAC/C,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAO;oBAAQ;gBAAQ;YAAE;QAC3E;QACA,iBAAiB,CAAC,OAAe;YAC/B,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAO;gBAAO;YAAE;QAClE;IACF;IAEA,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;GA3Ca;KAAA;AA8CN,MAAM,gBAAgB;;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Flashy/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}