'use client';

import React, { useState } from 'react';
import { Flashcard } from '@/types/flashcard';

interface FlashcardComponentProps {
  flashcard: Flashcard;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
  className?: string;
}

export const FlashcardComponent: React.FC<FlashcardComponentProps> = ({
  flashcard,
  onEdit,
  onDelete,
  showActions = true,
  className = '',
}) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.();
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this flashcard?')) {
      onDelete?.();
    }
  };

  return (
    <div className={`flashcard-container ${className}`}>
      <div
        className={`flashcard ${isFlipped ? 'flipped' : ''}`}
        onClick={handleFlip}
      >
        <div className="flashcard-inner">
          {/* Front side */}
          <div className="flashcard-front">
            <div className="flashcard-content">
              <div className="text-sm text-gray-500 mb-2">Front</div>
              <div className="flashcard-text">
                {flashcard.front || 'Click to add front content'}
              </div>
            </div>
            {showActions && (
              <div className="flashcard-actions">
                <button
                  onClick={handleEdit}
                  className="action-btn edit-btn"
                  title="Edit flashcard"
                >
                  ✏️
                </button>
                <button
                  onClick={handleDelete}
                  className="action-btn delete-btn"
                  title="Delete flashcard"
                >
                  🗑️
                </button>
              </div>
            )}
          </div>

          {/* Back side */}
          <div className="flashcard-back">
            <div className="flashcard-content">
              <div className="text-sm text-gray-500 mb-2">Back</div>
              <div className="flashcard-text">
                {flashcard.back || 'Click to add back content'}
              </div>
            </div>
            {showActions && (
              <div className="flashcard-actions">
                <button
                  onClick={handleEdit}
                  className="action-btn edit-btn"
                  title="Edit flashcard"
                >
                  ✏️
                </button>
                <button
                  onClick={handleDelete}
                  className="action-btn delete-btn"
                  title="Delete flashcard"
                >
                  🗑️
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="flip-hint">
        Click to flip
      </div>

      <style jsx>{`
        .flashcard-container {
          perspective: 1000px;
          width: 300px;
          height: 200px;
          margin: 10px;
        }

        .flashcard {
          position: relative;
          width: 100%;
          height: 100%;
          cursor: pointer;
          transition: transform 0.6s;
          transform-style: preserve-3d;
        }

        .flashcard.flipped {
          transform: rotateY(180deg);
        }

        .flashcard-inner {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .flashcard-front,
        .flashcard-back {
          position: absolute;
          width: 100%;
          height: 100%;
          backface-visibility: hidden;
          border-radius: 12px;
          border: 2px solid #e5e7eb;
          background: white;
          display: flex;
          flex-direction: column;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
        }

        .flashcard-front:hover,
        .flashcard-back:hover {
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
          border-color: #3b82f6;
        }

        .flashcard-back {
          transform: rotateY(180deg);
          background: #f8fafc;
        }

        .flashcard-content {
          flex: 1;
          padding: 20px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-align: center;
        }

        .flashcard-text {
          font-size: 16px;
          line-height: 1.5;
          color: #374151;
          word-wrap: break-word;
          overflow-wrap: break-word;
          max-height: 120px;
          overflow-y: auto;
        }

        .flashcard-actions {
          position: absolute;
          top: 8px;
          right: 8px;
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        .flashcard-front:hover .flashcard-actions,
        .flashcard-back:hover .flashcard-actions {
          opacity: 1;
        }

        .action-btn {
          width: 28px;
          height: 28px;
          border: none;
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.9);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .action-btn:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .edit-btn:hover {
          background: #dbeafe;
        }

        .delete-btn:hover {
          background: #fee2e2;
        }

        .flip-hint {
          text-align: center;
          margin-top: 8px;
          font-size: 12px;
          color: #6b7280;
          opacity: 0.7;
        }

        @media (max-width: 640px) {
          .flashcard-container {
            width: 280px;
            height: 180px;
          }

          .flashcard-content {
            padding: 16px;
          }

          .flashcard-text {
            font-size: 14px;
          }
        }
      `}</style>
    </div>
  );
};
