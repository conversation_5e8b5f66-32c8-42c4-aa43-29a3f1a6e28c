"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useFlashcards } from "@/context/FlashcardContext";
import { SetModal } from "@/components/SetModal";
import { useSampleData } from "@/hooks/useSampleData";

export default function Home() {
  const { sets, createSet, updateSet, deleteSet } = useFlashcards();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingSet, setEditingSet] = useState(null);

  // Initialize sample data for demonstration
  useSampleData();

  const handleCreateSet = (name: string, description?: string) => {
    createSet(name, description);
  };

  const handleEditSet = (set: any) => {
    setEditingSet(set);
  };

  const handleUpdateSet = (name: string, description?: string) => {
    if (editingSet) {
      updateSet(editingSet.id, { name, description });
      setEditingSet(null);
    }
  };

  const handleDeleteSet = (setId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this set? All flashcards in this set will be lost."
      )
    ) {
      deleteSet(setId);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">📚 Flashy</h1>
              <span className="ml-2 text-sm text-gray-500">
                Flashcard Learning App
              </span>
            </div>
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              + New Set
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {sets.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📚</div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              No flashcard sets yet
            </h2>
            <p className="text-gray-600 mb-6">
              Create your first flashcard set to start learning!
            </p>
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Create Your First Set
            </button>
          </div>
        ) : (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Your Flashcard Sets ({sets.length})
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sets.map((set) => (
                <div
                  key={set.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
                >
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {set.name}
                      </h3>
                      <div className="flex gap-1 ml-2">
                        <button
                          onClick={() => handleEditSet(set)}
                          className="text-gray-400 hover:text-blue-600 p-1"
                          title="Edit set"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDeleteSet(set.id)}
                          className="text-gray-400 hover:text-red-600 p-1"
                          title="Delete set"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>

                    {set.description && (
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {set.description}
                      </p>
                    )}

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{set.flashcards.length} cards</span>
                      <span>
                        Updated {new Date(set.updatedAt).toLocaleDateString()}
                      </span>
                    </div>

                    <Link
                      href={`/sets/${set.id}`}
                      className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors"
                    >
                      Study Set
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>

      {/* Modals */}
      <SetModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSave={handleCreateSet}
        title="Create New Flashcard Set"
      />

      <SetModal
        isOpen={!!editingSet}
        onClose={() => setEditingSet(null)}
        onSave={handleUpdateSet}
        set={editingSet}
        title="Edit Flashcard Set"
      />
    </div>
  );
}
