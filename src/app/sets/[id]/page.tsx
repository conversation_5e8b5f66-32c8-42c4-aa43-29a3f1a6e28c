'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useFlashcards } from '@/context/FlashcardContext';
import { FlashcardComponent } from '@/components/FlashcardComponent';
import { FlashcardModal } from '@/components/FlashcardModal';
import { Flashcard } from '@/types/flashcard';

export default function SetPage() {
  const params = useParams();
  const router = useRouter();
  const { sets, currentSet, setCurrentSet, createFlashcard, updateFlashcard, deleteFlashcard } = useFlashcards();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingCard, setEditingCard] = useState<Flashcard | null>(null);

  const setId = params.id as string;

  useEffect(() => {
    if (setId) {
      setCurrentSet(setId);
    }
  }, [setId, setCurrentSet]);

  useEffect(() => {
    // If set doesn't exist, redirect to home
    if (sets.length > 0 && !sets.find(set => set.id === setId)) {
      router.push('/');
    }
  }, [sets, setId, router]);

  const handleCreateCard = (front: string, back: string) => {
    if (setId) {
      createFlashcard(setId, front, back);
    }
  };

  const handleEditCard = (card: Flashcard) => {
    setEditingCard(card);
  };

  const handleUpdateCard = (front: string, back: string) => {
    if (editingCard && setId) {
      updateFlashcard(setId, editingCard.id, { front, back });
      setEditingCard(null);
    }
  };

  const handleDeleteCard = (cardId: string) => {
    if (setId) {
      deleteFlashcard(setId, cardId);
    }
  };

  if (!currentSet) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">🔍</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Set not found
          </h2>
          <p className="text-gray-600 mb-4">
            The flashcard set you're looking for doesn't exist.
          </p>
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link
                href="/"
                className="text-blue-600 hover:text-blue-700 mr-4"
              >
                ← Back
              </Link>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {currentSet.name}
                </h1>
                {currentSet.description && (
                  <p className="text-sm text-gray-600">
                    {currentSet.description}
                  </p>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <Link
                href={`/sets/${setId}/study`}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                📖 Study Mode
              </Link>
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                + Add Card
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentSet.flashcards.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📝</div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              No flashcards yet
            </h2>
            <p className="text-gray-600 mb-6">
              Add your first flashcard to start building this set!
            </p>
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Add Your First Card
            </button>
          </div>
        ) : (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Flashcards ({currentSet.flashcards.length})
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {currentSet.flashcards.map((card) => (
                <FlashcardComponent
                  key={card.id}
                  flashcard={card}
                  onEdit={() => handleEditCard(card)}
                  onDelete={() => handleDeleteCard(card.id)}
                  showActions={true}
                />
              ))}
            </div>
          </div>
        )}
      </main>

      {/* Modals */}
      <FlashcardModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSave={handleCreateCard}
        title="Add New Flashcard"
      />

      <FlashcardModal
        isOpen={!!editingCard}
        onClose={() => setEditingCard(null)}
        onSave={handleUpdateCard}
        flashcard={editingCard || undefined}
        title="Edit Flashcard"
      />
    </div>
  );
}
