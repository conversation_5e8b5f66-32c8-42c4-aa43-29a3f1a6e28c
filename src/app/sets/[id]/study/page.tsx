'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useFlashcards } from '@/context/FlashcardContext';
import { FlashcardComponent } from '@/components/FlashcardComponent';

export default function StudyPage() {
  const params = useParams();
  const router = useRouter();
  const { sets, currentSet, setCurrentSet } = useFlashcards();
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isShuffled, setIsShuffled] = useState(false);
  const [shuffledCards, setShuffledCards] = useState<any[]>([]);

  const setId = params.id as string;

  useEffect(() => {
    if (setId) {
      setCurrentSet(setId);
    }
  }, [setId, setCurrentSet]);

  useEffect(() => {
    // If set doesn't exist, redirect to home
    if (sets.length > 0 && !sets.find(set => set.id === setId)) {
      router.push('/');
    }
  }, [sets, setId, router]);

  useEffect(() => {
    if (currentSet && currentSet.flashcards.length > 0) {
      if (isShuffled) {
        const shuffled = [...currentSet.flashcards].sort(() => Math.random() - 0.5);
        setShuffledCards(shuffled);
      } else {
        setShuffledCards(currentSet.flashcards);
      }
      setCurrentCardIndex(0);
    }
  }, [currentSet, isShuffled]);

  const cards = shuffledCards.length > 0 ? shuffledCards : currentSet?.flashcards || [];
  const currentCard = cards[currentCardIndex];

  const handleNext = () => {
    if (currentCardIndex < cards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentCardIndex > 0) {
      setCurrentCardIndex(currentCardIndex - 1);
    }
  };

  const handleShuffle = () => {
    setIsShuffled(!isShuffled);
  };

  const handleRestart = () => {
    setCurrentCardIndex(0);
  };

  if (!currentSet) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">🔍</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Set not found
          </h2>
          <p className="text-gray-600 mb-4">
            The flashcard set you're looking for doesn't exist.
          </p>
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  if (currentSet.flashcards.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">📝</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No cards to study
          </h2>
          <p className="text-gray-600 mb-4">
            Add some flashcards to this set before studying.
          </p>
          <Link
            href={`/sets/${setId}`}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Add Cards
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link
                href={`/sets/${setId}`}
                className="text-blue-600 hover:text-blue-700 mr-4"
              >
                ← Back to Set
              </Link>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Study: {currentSet.name}
                </h1>
                <p className="text-sm text-gray-600">
                  Card {currentCardIndex + 1} of {cards.length}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleShuffle}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  isShuffled
                    ? 'bg-orange-600 hover:bg-orange-700 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                }`}
              >
                🔀 {isShuffled ? 'Shuffled' : 'Shuffle'}
              </button>
              <button
                onClick={handleRestart}
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors"
              >
                🔄 Restart
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Study Interface */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col items-center">
          {/* Progress Bar */}
          <div className="w-full max-w-md mb-8">
            <div className="bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${((currentCardIndex + 1) / cards.length) * 100}%`,
                }}
              />
            </div>
            <div className="text-center text-sm text-gray-600 mt-2">
              Progress: {currentCardIndex + 1} / {cards.length}
            </div>
          </div>

          {/* Flashcard */}
          <div className="mb-8">
            <FlashcardComponent
              flashcard={currentCard}
              showActions={false}
              className="scale-110"
            />
          </div>

          {/* Navigation Controls */}
          <div className="flex gap-4">
            <button
              onClick={handlePrevious}
              disabled={currentCardIndex === 0}
              className="bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors"
            >
              ← Previous
            </button>
            
            {currentCardIndex === cards.length - 1 ? (
              <Link
                href={`/sets/${setId}`}
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                ✅ Complete Study
              </Link>
            ) : (
              <button
                onClick={handleNext}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Next →
              </button>
            )}
          </div>

          {/* Keyboard Shortcuts Hint */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>💡 Tip: Click the card to flip it</p>
            <p className="mt-1">Use arrow keys: ← Previous | → Next</p>
          </div>
        </div>
      </main>

      {/* Keyboard Navigation */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            document.addEventListener('keydown', function(e) {
              if (e.key === 'ArrowLeft' && ${currentCardIndex} > 0) {
                document.querySelector('[data-action="previous"]')?.click();
              } else if (e.key === 'ArrowRight' && ${currentCardIndex} < ${cards.length - 1}) {
                document.querySelector('[data-action="next"]')?.click();
              }
            });
          `,
        }}
      />
    </div>
  );
}
