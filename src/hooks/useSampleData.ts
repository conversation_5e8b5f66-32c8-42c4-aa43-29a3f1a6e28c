import { useEffect } from 'react';
import { useFlashcards } from '@/context/FlashcardContext';

export const useSampleData = () => {
  const { sets, createSet, createFlashcard } = useFlashcards();

  useEffect(() => {
    // Only create sample data if no sets exist
    if (sets.length === 0) {
      // Create a sample Spanish vocabulary set
      createSet(
        'Spanish Vocabulary',
        'Basic Spanish words and phrases for beginners'
      );

      // Create a sample JavaScript concepts set
      createSet(
        'JavaScript Fundamentals',
        'Core JavaScript concepts every developer should know'
      );

      // Create a sample History set
      createSet(
        'World History',
        'Important historical events and dates'
      );
    }
  }, [sets, createSet]);

  useEffect(() => {
    // Add sample flashcards to the sets after they're created
    if (sets.length > 0) {
      const spanishSet = sets.find(set => set.name === 'Spanish Vocabulary');
      const jsSet = sets.find(set => set.name === 'JavaScript Fundamentals');
      const historySet = sets.find(set => set.name === 'World History');

      // Add Spanish vocabulary cards
      if (spanishSet && spanishSet.flashcards.length === 0) {
        const spanishCards = [
          { front: 'Hello', back: 'Hola' },
          { front: 'Goodbye', back: 'Adiós' },
          { front: 'Thank you', back: 'Gracias' },
          { front: 'Please', back: 'Por favor' },
          { front: 'Yes', back: 'Sí' },
          { front: 'No', back: 'No' },
          { front: 'Water', back: 'Agua' },
          { front: 'Food', back: 'Comida' },
          { front: 'House', back: 'Casa' },
          { front: 'Friend', back: 'Amigo/Amiga' },
        ];

        spanishCards.forEach(card => {
          createFlashcard(spanishSet.id, card.front, card.back);
        });
      }

      // Add JavaScript concept cards
      if (jsSet && jsSet.flashcards.length === 0) {
        const jsCards = [
          {
            front: 'What is a closure in JavaScript?',
            back: 'A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.'
          },
          {
            front: 'What is the difference between let, const, and var?',
            back: 'var is function-scoped and can be redeclared. let is block-scoped and can be reassigned. const is block-scoped and cannot be reassigned.'
          },
          {
            front: 'What is event bubbling?',
            back: 'Event bubbling is when an event starts from the target element and bubbles up to the parent elements in the DOM hierarchy.'
          },
          {
            front: 'What is the difference between == and ===?',
            back: '== performs type coercion and compares values. === compares both value and type without coercion.'
          },
          {
            front: 'What is a Promise?',
            back: 'A Promise is an object representing the eventual completion or failure of an asynchronous operation.'
          },
          {
            front: 'What is the purpose of async/await?',
            back: 'async/await provides a way to write asynchronous code that looks and behaves more like synchronous code.'
          },
        ];

        jsCards.forEach(card => {
          createFlashcard(jsSet.id, card.front, card.back);
        });
      }

      // Add History cards
      if (historySet && historySet.flashcards.length === 0) {
        const historyCards = [
          {
            front: 'When did World War II end?',
            back: 'September 2, 1945 (with Japan\'s surrender)'
          },
          {
            front: 'Who was the first person to walk on the moon?',
            back: 'Neil Armstrong on July 20, 1969'
          },
          {
            front: 'When did the Berlin Wall fall?',
            back: 'November 9, 1989'
          },
          {
            front: 'What year did the American Civil War begin?',
            back: '1861'
          },
          {
            front: 'Who wrote the Declaration of Independence?',
            back: 'Thomas Jefferson (primary author)'
          },
          {
            front: 'When did the French Revolution begin?',
            back: '1789'
          },
        ];

        historyCards.forEach(card => {
          createFlashcard(historySet.id, card.front, card.back);
        });
      }
    }
  }, [sets, createFlashcard]);
};
