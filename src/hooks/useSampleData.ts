import { useEffect, useRef } from "react";
import { useFlashcards } from "@/context/FlashcardContext";

export const useSampleData = () => {
  const { createSet, createFlashcard } = useFlashcards();
  const hasInitialized = useRef(false);

  useEffect(() => {
    // Only run once and only if no data exists in localStorage
    if (hasInitialized.current) {
      return;
    }

    // Check if data already exists in localStorage
    const existingData = localStorage.getItem("flashcard-sets");
    if (existingData && JSON.parse(existingData).length > 0) {
      hasInitialized.current = true;
      return;
    }

    hasInitialized.current = true;

    // Create sample data
    const sampleData = [
      {
        name: "Spanish Vocabulary",
        description: "Basic Spanish words and phrases for beginners",
        cards: [
          { front: "Hello", back: "Hola" },
          { front: "Goodbye", back: "Adiós" },
          { front: "Thank you", back: "Gracias" },
          { front: "Please", back: "Por favor" },
          { front: "Yes", back: "Sí" },
          { front: "No", back: "No" },
          { front: "Water", back: "Agua" },
          { front: "Food", back: "Comida" },
          { front: "House", back: "Casa" },
          { front: "Friend", back: "Amigo/Amiga" },
        ],
      },
      {
        name: "JavaScript Fundamentals",
        description: "Core JavaScript concepts every developer should know",
        cards: [
          {
            front: "What is a closure in JavaScript?",
            back: "A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.",
          },
          {
            front: "What is the difference between let, const, and var?",
            back: "var is function-scoped and can be redeclared. let is block-scoped and can be reassigned. const is block-scoped and cannot be reassigned.",
          },
          {
            front: "What is event bubbling?",
            back: "Event bubbling is when an event starts from the target element and bubbles up to the parent elements in the DOM hierarchy.",
          },
          {
            front: "What is the difference between == and ===?",
            back: "== performs type coercion and compares values. === compares both value and type without coercion.",
          },
          {
            front: "What is a Promise?",
            back: "A Promise is an object representing the eventual completion or failure of an asynchronous operation.",
          },
          {
            front: "What is the purpose of async/await?",
            back: "async/await provides a way to write asynchronous code that looks and behaves more like synchronous code.",
          },
        ],
      },
      {
        name: "World History",
        description: "Important historical events and dates",
        cards: [
          {
            front: "When did World War II end?",
            back: "September 2, 1945 (with Japan's surrender)",
          },
          {
            front: "Who was the first person to walk on the moon?",
            back: "Neil Armstrong on July 20, 1969",
          },
          {
            front: "When did the Berlin Wall fall?",
            back: "November 9, 1989",
          },
          {
            front: "What year did the American Civil War begin?",
            back: "1861",
          },
          {
            front: "Who wrote the Declaration of Independence?",
            back: "Thomas Jefferson (primary author)",
          },
          {
            front: "When did the French Revolution begin?",
            back: "1789",
          },
        ],
      },
    ];

    // Create sets and cards sequentially to avoid race conditions
    const createSampleData = async () => {
      for (const setData of sampleData) {
        createSet(setData.name, setData.description);

        // Small delay to ensure set is created
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Get the created set ID from localStorage
        const currentSets = JSON.parse(
          localStorage.getItem("flashcard-sets") ?? "[]"
        );
        const createdSet = currentSets.find(
          (set: any) => set.name === setData.name
        );

        if (createdSet) {
          for (const card of setData.cards) {
            createFlashcard(createdSet.id, card.front, card.back);
            // Small delay between card creation
            await new Promise((resolve) => setTimeout(resolve, 10));
          }
        }
      }
    };

    createSampleData();
  }, []); // Empty dependency array - only run once
};
